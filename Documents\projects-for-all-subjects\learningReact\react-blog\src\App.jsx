import { useState } from 'react';
import './App.css';
import ToDo from './ToDO';

function App() {
  const [count, setCount] = useState(0);
  let a = 10;
  let b = 20;
  let name = "mia";

  function addab(x, y) {
    return x + y;
  }

  function operation(a, b, op) {
    if (op === '+') {
      return a + b;
    } else if (op === '-') {
      return a - b;
    } else {
      return 'Invalid op';
    }
  }

  return (
    <div>
      <ToDo />
      <h1>Counter val: {count}</h1>
      <button onClick={() => setCount(count + 1)}>Increase Counter val</button>
      <h1>{name}</h1>
      <h1>{name ? name : "user not found"}</h1>
      <h1>{a + b}</h1>
      <h1>{addab(40, 29)}</h1>
      <h2>
        <div>Op + : {operation(10, 25, '+')}</div>
        <div>Op - : {operation(10, 25, '-')}</div>
      </h2>
    </div>
  );
}

export default App;
// Note: The above code is a simple React application that includes a ToDo component and a counter.
// It demonstrates the use of state, functions, and conditional rendering in React.
// The ToDo component is imported and displayed, and the main App component manages a counter and performs basic arithmetic operations.
// The code also includes a simple function to add two numbers and another function to
