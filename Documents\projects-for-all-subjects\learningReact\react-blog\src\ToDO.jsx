function ToDo() {
  function callfunction() {
    alert("function is called");
  }

  return (
    <div>
      <h2>My name is <PERSON></h2>
      <img
        src="https://images.unsplash.com/photo-1741766135837-03019081fc72?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
        alt="img"
      />
      <ul>
        <li>Try to invest into the new traffic lights</li>
        <li>Rephrase a movie scene</li>
        <li>Improve the light spectrum</li>
      </ul>
      <button onClick={callfunction}>click me</button>
    </div>
  );
}

export default ToDo; // ✅ Default export is required for import to work
